<!DOCTYPE html>
<html>
  <head>
    <!-- <PERSON>e JS -->
    <script src="https://js.stripe.com/v3/"></script>
    <style>
      .form-container {
        width: 320px;
        margin: auto;
        background: #fff;
        padding: 24px;
        border-radius: 16px;
        font-family: sans-serif;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
      }

      .form-group {
        margin-bottom: 16px;
      }

      .form-label {
        font-weight: 600;
        margin-bottom: 6px;
        display: block;
      }

      input,
      select {
        width: 100%;
        padding: 12px;
        border: 1px solid #ccc;
        border-radius: 8px;
        font-size: 16px;
      }

      #card-element {
        padding: 12px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background: white;
      }

      #submit-button {
        margin-top: 20px;
        background-color: #005cff;
        color: white;
        font-size: 18px;
        padding: 12px;
        width: 100%;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #submit-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      #payment-message,
      #promo-message {
        margin-top: 14px;
        text-align: center;
        font-weight: bold;
      }

      #payment-request-button {
        margin-bottom: 20px;
      }

      .email-warning-text {
        font-size: 10px;
        color: black;
      }

      /* Loader styles */
      .loader {
        display: none;
        border: 3px solid #fff;
        border-top: 3px solid transparent;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      #submit-button.loading .loader {
        display: inline-block;
      }

      #submit-button.loading span:not(.loader) {
        display: none;
      }

      /* Promo code container */
      .promo-code-container {
        display: flex;
        gap: 8px;
      }

      #promo-code {
        flex: 1;
      }

      #apply-promo-button,
      #remove-promo-button {
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      }

      #apply-promo-button {
        background-color: #28a745;
        color: white;
      }

      #remove-promo-button {
        background-color: #dc3545;
        color: white;
        display: none;
      }

      #apply-promo-button:disabled,
      #remove-promo-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <div class="form-container">
      <!-- Apple Pay / Google Pay Button -->
      <div id="payment-request-button"></div>

      <!-- Card Payment Form -->
      <form id="ticket-payment-form">
        <div class="form-group">
          <label class="form-label">Cardholder Name</label>
          <input
            type="text"
            id="cardholder-name"
            placeholder="John Doe"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">Card Information</label>
          <div id="card-element"></div>
        </div>

        <div class="form-group">
          <label class="form-label">Billing Country</label>
          <select id="billing-country" required>
            <option value="GB">United Kingdom</option>
          </select>
        </div>

        <!-- Promo Code Input with Apply/Remove Buttons -->
        <div class="form-group">
          <label class="form-label">Promo Code</label>
          <div class="promo-code-container">
            <input type="text" id="promo-code" placeholder="Enter promo code" />
            <button type="button" id="apply-promo-button">Apply</button>
            <button type="button" id="remove-promo-button">Remove</button>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">
            <input type="checkbox" id="privacy-agreement" required style="width: auto; margin-right: 8px;" />
            I agree to the <a href="https://www.pyxi.ai/terms-and-conditions" target="_blank" class="text-blue-500 hover:underline">Terms & Conditions</a>
          </label>
        </div>

        <button id="submit-button" class="loading" disabled>
          <span id="payment-amount">Pay £0.00</span>
          <span class="loader"></span>
        </button>
        <div id="payment-message"></div>
        <div id="promo-message"></div>
      </form>
    </div>

    <script>
      let baseUrl = "https://pyxi-image-qa-dn66uttsza-nw.a.run.app/";
      const stripeKey =
        "pk_test_51PdxCZGcOMBQdhKJrUDGxWnxuHTPExZHINfWZKBpywSIV9Zh0VrppA3zmsNYB3BN2TyrjF0B0iNW3kaFAyTrAcmf003ulPBguZ";
      const eventIds = ['5381', '5380']
      const formId = "gRsmwvvE";
      let eventCount = 1;
      let userResponse = null;
      let eventId = null;
      let currentEvent = null;
      let appliedPromoCode = null;
      let ticketPrice = 0; // Initialize to 0 until fetched
      let displayPrice = "£0.00";

      function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }

      const isEmailSource = getQueryParam("source") === "email";

      // Function to get the _fbp cookie (Browser ID)
      function getFbpCookie() {
        const match = document.cookie.match("(^|;)\\s*_fbp\\s*=\\s*([^;]+)");
        return match ? match.pop() : "";
      }

      // Collect user data for the create-payment-intent API
      async function collectUserData() {
        const userAgent = navigator.userAgent;
        const fbp = getFbpCookie();
        const externalId = userResponse?.uid || "unknown";

        const ipAddress = await fetch("https://api.ipify.org?format=json")
          .then((res) => res.json())
          .then((data) => data.ip)
          .catch(() => null);

        return {
          fbc: getQueryParam("fbclid"),
          client_ip_address: ipAddress,
          client_user_agent: userAgent,
          fbp: fbp,
          external_id: externalId,
        };
      }

      function parseDinnerDate(dinnerDate) {
        dinnerDate = dinnerDate.split("*").join("");
        if (dinnerDate.split('-').length > 2) {
            dinnerDate = dinnerDate.substring(0, dinnerDate.lastIndexOf('-')).trim()
        }
        const months = {
          January: "01",
          February: "02",
          March: "03",
          April: "04",
          May: "05",
          June: "06",
          July: "07",
          Augu: "08",
          Sept: "09",
          October: "10",
          November: "11",
          December: "12",
        };
        const [_, datePart] = dinnerDate.split(" - ");
        const [month, day] = datePart
          .replace("rd", "")
          .replace("th", "")
          .replace("nd", "")
          .replace("st", "")
          .replace("st", "")
          .split(" ");
        const year = new Date().getFullYear();
        return `${year}-${months[month]}-${day.padStart(2, "0")}`;
      }

      async function createUserFromData(eventId) {
        const responseId =
          getQueryParam("response_id") || "default_response_id";

        try {
          const res = await fetch(
            `${baseUrl}users/external?typeform_form_id=${formId}&typeform_response_id=${responseId}&event_id=${eventId}`,
            {
              method: "POST",
              headers: { accept: "application/json" },
              body: "",
            }
          );

          if (!res.ok) {
            console.error("API call failed:", res.statusText);
          } else {
            userResponse = await res.json();
            console.log("API call successful:", userResponse);
          }
        } catch (error) {
          console.error("Error calling API:", error);
        }
      }

      function getTicketId() {
        return currentEvent?.tickets?.length > 0
          ? currentEvent.tickets[0].ticket_id
          : "";
      }

      async function getEventData(eventId) {
        const submitButton = document.getElementById("submit-button");
        try {
          const eventRes = await fetch(`${baseUrl}public-events/${eventId}`, {
            method: "GET",
            headers: { accept: "application/json" },
          });

          if (!eventRes.ok) {
            console.error(
              "Failed to fetch event details:",
              eventRes.statusText
            );
            submitButton.classList.remove("loading");
            submitButton.disabled = true;
            return null;
          }

          const eventData = await eventRes.json();
          console.log(eventData, "currentEvent");

          currentEvent = eventData;
          // Update ticket price from currentEvent
          if (currentEvent?.tickets?.length > 0) {
            ticketPrice = Math.round(currentEvent.tickets[0].price * 100); // Convert to pence
            displayPrice = `£${(ticketPrice / 100).toFixed(2)}`;
            document.getElementById(
              "payment-amount"
            ).textContent = `Pay ${displayPrice}`;
            // Remove loader and enable button
            submitButton.classList.remove("loading");
            submitButton.disabled = false;

            if (isEmailSource) {
              applyPromoCode("RESET20");

              document.getElementById("promo-code").disabled = true;
              document.getElementById("promo-code").value = "RESET20";
            }
          } else {
            // No tickets available, keep loader off but disable button
            submitButton.classList.remove("loading");
            submitButton.disabled = true;
          }
        } catch (error) {
          console.error("Error fetching event details:", error);
          submitButton.classList.remove("loading");
          submitButton.disabled = true;
          return null;
        }
      }

      async function getEventIdByDate(dinnerDate) {
        const selectedDate = parseDinnerDate(dinnerDate);
        console.log(selectedDate, "dinnerDate");

        // Fetch all event data in parallel
        const fetchPromises = eventIds.map(id =>
          fetch(`${baseUrl}public-events/${id}`, {
            method: "GET",
            headers: { accept: "application/json" },
          })
            .then(res => res.ok ? res.json() : null)
            .catch(error => {
              console.error(`Error fetching event details for eventId ${id}:`, error);
              return null;
            })
        );

        // Wait for all fetches to complete
        const allEventData = await Promise.all(fetchPromises);

        // Combine all recurrence events
        let allRecurrenceEvents = [];
        for (const eventData of allEventData) {
          if (eventData && eventData.recurrence_events) {
            allRecurrenceEvents = allRecurrenceEvents.concat(eventData.recurrence_events);
          }
        }

        // Find the matching event by date
        const matchingEvent = allRecurrenceEvents.find(event => {
          const eventDate = event.start_date.split("T")[0];
          return eventDate === selectedDate;
        });

        if (matchingEvent) {
          eventId = matchingEvent.event_id;
          await getEventData(eventId);
          await createUserFromData(eventId);
          console.log("Extracted event_id:", eventId);
          return eventId;
        } else {
          console.error("No matching event found for date:", selectedDate);
          return null;
        }
      }

      async function checkIsPlusOneSelect(plusOneOption) {
        const yesOption =  `Yes and I'll also pay now for both (£13.99 now for both tickets)`;
        const noOption = `No. Just continue to payment (£8.99 now)`;
        if (plusOneOption === yesOption) {
            eventCount = 2;
            applyPromoCode("RESET20");
        }
      }

      async function getTypeformData() {
        const responseId =
          getQueryParam("response_id") || "default_response_id";
        try {
          const res = await fetch(
            `${baseUrl}/type-form-data?typeform_form_id=${formId}&typeform_response_id=${responseId}`,
            { method: "GET", headers: { accept: "application/json" } }
          );

          if (!res.ok) {
            console.error("API call failed:", res.statusText);
          } else {
            const response = await res.json();
            if (response.responses?.["*Select Dinner Date*"]) {
              const label = response.responses["*Select Dinner Date*"];
              console.log("Label for field 1BQSsnMiRzCy:", label);
              const plusOneOption = response.responses['* (Optional) Would you like to bring a +1 to the dinner?*'];
              checkIsPlusOneSelect(plusOneOption);
              await getEventIdByDate(label);
            } else {
              alert("Date not selected.");
            }
          }
        } catch (error) {
          console.error("Error calling API:", error);
        }
      }

      // Function to apply promo code
      function applyPromoCode(promoCodeName) {
        if (!currentEvent?.promo_codes?.length) {
          document.getElementById("promo-message").textContent =
            "No promo codes available.";
          document.getElementById("promo-message").style.color = "red";
          return false;
        }

        const promo = currentEvent.promo_codes.find(
          (code) => code.name.toUpperCase() === promoCodeName.toUpperCase()
        );

        if (!promo) {
          document.getElementById("promo-message").textContent =
            "Invalid promo code.";
          document.getElementById("promo-message").style.color = "red";
          return false;
        }

        if (
          promo.minimum_amount > currentEvent.tickets[0].price ||
          promo.minimum_tickets > 1
        ) {
          document.getElementById("promo-message").textContent =
            "Promo code requirements not met.";
          document.getElementById("promo-message").style.color = "red";
          return false;
        }

        // Apply discount
        const originalPrice = currentEvent.tickets[0].price * eventCount;
        const discount = (promo.discount_percentage / 100) * originalPrice;
        const discountedPrice = originalPrice - discount;
        ticketPrice = Math.round(discountedPrice * 100); // Convert to pence
        displayPrice = `£${discountedPrice.toFixed(2)}`;
        document.getElementById(
          "payment-amount"
        ).textContent = `Pay ${displayPrice}`;
        document.getElementById(
          "promo-message"
        ).textContent = `Promo code ${promo.name} applied!`;
        document.getElementById("promo-message").style.color = "green";

        appliedPromoCode = promo.promo_code_id;

        // Disable promo code input and toggle buttons
        document.getElementById("promo-code").disabled = true;
        document.getElementById("apply-promo-button").style.display = "none";
        document.getElementById("remove-promo-button").style.display =
          "inline-block";

        // Update payment request amount
        paymentRequest.update({
          total: { label: "Ticket Payment", amount: ticketPrice },
        });

        return true;
      }

      // Function to remove promo code
      function removePromoCode() {
        // Reset to original price from currentEvent
        if (currentEvent?.tickets?.length > 0) {
          ticketPrice = Math.round(currentEvent.tickets[0].price * 100);
          displayPrice = `£${(ticketPrice / 100).toFixed(2)}`;
        } else {
          ticketPrice = 0;
          displayPrice = "£0.00";
        }
        document.getElementById(
          "payment-amount"
        ).textContent = `Pay ${displayPrice}`;
        document.getElementById("promo-message").textContent = "";
        appliedPromoCode = null;

        // Re-enable promo code input and toggle buttons
        document.getElementById("promo-code").disabled = false;
        document.getElementById("promo-code").value = "";
        document.getElementById("apply-promo-button").style.display =
          "inline-block";
        document.getElementById("remove-promo-button").style.display = "none";

        // Update payment request amount
        paymentRequest.update({
          total: { label: "Ticket Payment", amount: ticketPrice },
        });
      }

      // Add event listener for apply promo button
      document
        .getElementById("apply-promo-button")
        .addEventListener("click", () => {
          const promoCodeName = document
            .getElementById("promo-code")
            .value.trim();
          const applyButton = document.getElementById("apply-promo-button");
          applyButton.disabled = true;

          if (!promoCodeName) {
            document.getElementById("promo-message").textContent =
              "Please enter a promo code.";
            document.getElementById("promo-message").style.color = "red";
            applyButton.disabled = false;
            return;
          }

          if (!currentEvent?.tickets?.length) {
            document.getElementById("promo-message").textContent =
              "Event data not loaded.";
            document.getElementById("promo-message").style.color = "red";
            applyButton.disabled = false;
            return;
          }

          applyPromoCode(promoCodeName);
          applyButton.disabled = false;
        });

      // Add event listener for remove promo button
      document
        .getElementById("remove-promo-button")
        .addEventListener("click", () => {
          const removeButton = document.getElementById("remove-promo-button");
          removeButton.disabled = true;
          removePromoCode();
          removeButton.disabled = false;
        });

      document.addEventListener("DOMContentLoaded", async () => {
        await getTypeformData();
      });

      const stripe = Stripe(stripeKey);
      const elements = stripe.elements();

      const paymentRequest = stripe.paymentRequest({
        country: "GB",
        currency: "gbp",
        total: { label: "Ticket Payment", amount: ticketPrice },
        requestPayerName: true,
        requestPayerEmail: true,
      });

      const prButton = elements.create("paymentRequestButton", {
        paymentRequest,
        style: {
          paymentRequestButton: {
            type: "default",
            theme: "dark",
            height: "45px",
          },
        },
      });

      paymentRequest.canMakePayment().then((result) => {
        if (result) {
          prButton.mount("#payment-request-button");
        } else {
          document.getElementById("payment-request-button").style.display =
            "none";
        }
      });

      paymentRequest.on("paymentmethod", async (ev) => {
        const responseId =
          getQueryParam("response_id") || "default_response_id";
        const ticket_id = getTicketId();
        if (!ticket_id) {
          alert("Invalid ticket ID!");
          return;
        }

        const userData = await collectUserData();
        const res = await fetch(
          `${baseUrl}events/${eventId}/create-payment-intent`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              authorization: userResponse?.token,
              authorization,
            },
            body: JSON.stringify({
              tickets: [{ ticket_id, count: eventCount }],
              promo_code_id: appliedPromoCode ? appliedPromoCode : undefined,
              typeform_form_id: formId,
              typeform_response_id: responseId,
              user_data: userData,
            }),
          }
        );

        const { client_secret } = await res.json();
        const { error } = await stripe.confirmCardPayment(
          client_secret,
          { payment_method: ev.paymentMethod.id },
          { handleActions: false }
        );

        if (error) {
          ev.complete("fail");
          alert(error.message);
        } else {
          ev.complete("success");
          alert("✅ Payment successful via Apple Pay / Google Pay!");
        }
      });

      const cardElement = elements.create("card");
      cardElement.mount("#card-element");

      const form = document.getElementById("ticket-payment-form");
      form.addEventListener("submit", async (e) => {
        e.preventDefault();

        const submitButton = document.getElementById("submit-button");
        submitButton.classList.add("loading");
        submitButton.disabled = true;
        document.getElementById("payment-message").textContent = "";

        const cardholderName = document.getElementById("cardholder-name").value;
        const billingCountry = document.getElementById("billing-country").value;
        const responseId =
          getQueryParam("response_id") || "default_response_id";
        const ticket_id = getTicketId();

        if (!ticket_id) {
          submitButton.classList.remove("loading");
          submitButton.disabled = false;
          alert("Something went wrong!");
          return;
        }

        try {
          const userData = await collectUserData();
          const res = await fetch(
            `${baseUrl}events/${eventId}/create-payment-intent`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                authorization: userResponse?.token,
              },
              body: JSON.stringify({
                tickets: [{ ticket_id, count: eventCount }],
                promo_code_id: appliedPromoCode ? appliedPromoCode : undefined,
                typeform_form_id: formId,
                typeform_response_id: responseId,
                user_data: userData,
              }),
            }
          );

          const { client_secret } = await res.json();
          const result = await stripe.confirmCardPayment(client_secret, {
            payment_method: {
              card: cardElement,
              billing_details: {
                name: cardholderName,
                address: { country: billingCountry },
              },
            },
          });

          if (result.error) {
            document.getElementById("payment-message").textContent =
              result.error.message;
            document.getElementById("payment-message").style.color = "red";
          } else if (result.paymentIntent.status === "succeeded") {
            document.getElementById("payment-message").textContent =
              "🎉 Payment successful!";
            document.getElementById("payment-message").style.color = "green";
            window.location.href = "/confirmed";
          }
        } catch (error) {
          document.getElementById("payment-message").textContent =
            "An error occurred. Please try again.";
          document.getElementById("payment-message").style.color = "red";
        } finally {
          submitButton.classList.remove("loading");
          submitButton.disabled = false;
        }
      });
    </script>
  </body>
</html>